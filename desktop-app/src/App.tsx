import {
  ToastProvider,
  To<PERSON>,
  ToastTitle,
  ToastDescription,
  ToastMessage,
  ToastVariant
} from "./components/ui/toast"
import Queue from "./_pages/Queue"
import { ToastViewport } from "@radix-ui/react-toast"
import { useEffect, useRef, useState } from "react"
import Solutions from "./_pages/Solutions"
import { QueryClient, QueryClientProvider } from "react-query"
import ClerkA<PERSON> from "./components/ClerkAuth"
import UserProfile from "./components/UserProfile"
import { createContext, useContext } from "react"
import { useUser, useClerk } from "@clerk/clerk-react"

declare global {
  interface Window {
    electronAPI: {
      //RANDOM GETTER/SETTERS
      updateContentDimensions: (dimensions: {
        width: number
        height: number
      }) => Promise<void>
      getScreenshots: () => Promise<Array<{ path: string; preview: string }>>
      getApiKey: () => Promise<string | null>
      clearStore: () => Promise<{ success: boolean; error?: string }>

      //GLOBAL EVENTS
      onUnauthorized: (callback: () => void) => () => void
      onApiKeyOutOfCredits: (callback: () => void) => () => void
      onScreenshotTaken: (
        callback: (data: { path: string; preview: string }) => void
      ) => () => void
      onProcessingNoScreenshots: (callback: () => void) => () => void
      onResetView: (callback: () => void) => () => void
      takeScreenshot: () => Promise<void>

      //INITIAL SOLUTION EVENTS
      deleteScreenshot: (
        path: string
      ) => Promise<{ success: boolean; error?: string }>
      onSolutionStart: (callback: () => void) => () => void
      onSolutionError: (callback: (error: string) => void) => () => void
      onSolutionSuccess: (callback: (data: any) => void) => () => void
      onProblemExtracted: (callback: (data: any) => void) => () => void

      onDebugSuccess: (callback: (data: any) => void) => () => void

      onDebugStart: (callback: () => void) => () => void
      onDebugError: (callback: (error: string) => void) => () => void

      // Add the updateApiKey method
      updateApiKey: (apiKey: string) => Promise<void>
      setApiKey: (
        apiKey: string
      ) => Promise<{ success: boolean; error?: string }>

      openExternal: (url: string) => Promise<void>
    }
  }
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: Infinity,
      cacheTime: Infinity
    }
  }
})

interface ToastContextType {
  showToast: (title: string, description: string, variant: ToastVariant) => void
}

export const ToastContext = createContext<ToastContextType | undefined>(
  undefined
)

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

const App: React.FC = () => {
  const [view, setView] = useState<"queue" | "solutions" | "debug">("queue")
  const [isProfileComplete, setIsProfileComplete] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [toastOpen, setToastOpen] = useState(false)
  const [toastMessage, setToastMessage] = useState<ToastMessage>({
    title: "",
    description: "",
    variant: "neutral"
  })

  const { isSignedIn, user, isLoaded } = useUser()
  const { signOut } = useClerk()

  const handleAuthSuccess = () => {
    // Check if user already has API key configured
    checkUserProfile()
  }

  const checkUserProfile = async () => {
    try {
      // Check if API key exists locally
      const apiKey = await window.electronAPI.getApiKey()
      if (apiKey) {
        setIsProfileComplete(true)
        return
      }

      // Check Clerk user metadata
      if (user?.publicMetadata?.openaiApiKey) {
        // Store in electron store for local access
        await window.electronAPI.setApiKey(user.publicMetadata.openaiApiKey as string)
        setIsProfileComplete(true)
      }
    } catch (error) {
      console.error("Error checking user profile:", error)
    }
  }

  const handleApiKeySet = () => {
    setIsProfileComplete(true)
  }

  const handleSignOut = async () => {
    console.log("App handleSignOut called!")
    try {
      console.log("Signing out from Clerk...")
      // Sign out from Clerk
      await signOut()

      console.log("Clearing local state...")
      // Clear local state
      setIsProfileComplete(false)
      await window.electronAPI.clearStore()

      console.log("Sign out completed successfully")
      showToast("Signed Out", "You have been successfully signed out.", "success")
    } catch (error) {
      console.error("Error signing out:", error)
      // Force local sign out even if Clerk call fails
      console.log("Forcing local sign out...")
      setIsProfileComplete(false)
      await window.electronAPI.clearStore()
    }
  }

  const showToast = (
    title: string,
    description: string,
    variant: ToastVariant
  ) => {
    setToastMessage({ title, description, variant })
    setToastOpen(true)
  }

  // Effect for height monitoring

  useEffect(() => {
    const cleanup = window.electronAPI.onResetView(() => {
      queryClient.invalidateQueries(["screenshots"])
      queryClient.invalidateQueries(["problem_statement"])
      queryClient.invalidateQueries(["solution"])
      queryClient.invalidateQueries(["new_solution"])
      setView("queue")
    })

    return () => {
      cleanup()
    }
  }, [])

  useEffect(() => {
    if (!containerRef.current) return

    const updateHeight = () => {
      if (!containerRef.current) return
      const height = containerRef.current.scrollHeight
      const width = containerRef.current.scrollWidth
      window.electronAPI?.updateContentDimensions({ width, height })
    }

    const resizeObserver = new ResizeObserver(() => {
      updateHeight()
    })

    // Initial height update
    updateHeight()

    // Observe for changes
    resizeObserver.observe(containerRef.current)

    // Also update height when view changes
    const mutationObserver = new MutationObserver(() => {
      updateHeight()
    })

    mutationObserver.observe(containerRef.current, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true
    })

    return () => {
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    }
  }, [view]) // Re-run when view changes
  useEffect(() => {
    const cleanupFunctions = [
      window.electronAPI.onSolutionStart(() => {
        setView("solutions")
      }),

      window.electronAPI.onUnauthorized(() => {
        queryClient.removeQueries(["screenshots"])
        queryClient.removeQueries(["solution"])
        queryClient.removeQueries(["problem_statement"])
        setView("queue")
      }),
      // Update this reset handler
      window.electronAPI.onResetView(() => {
        queryClient.removeQueries(["screenshots"])
        queryClient.removeQueries(["solution"])
        queryClient.removeQueries(["problem_statement"])
        setView("queue")
      }),
      window.electronAPI.onProblemExtracted((data: any) => {
        if (view === "queue") {
          queryClient.invalidateQueries(["problem_statement"])
          queryClient.setQueryData(["problem_statement"], data)
        }
      })
    ]
    return () => cleanupFunctions.forEach((cleanup) => cleanup())
  }, [])

  useEffect(() => {
    // Check user profile when Clerk user changes
    if (isSignedIn && user && isLoaded) {
      checkUserProfile()
    } else if (!isSignedIn && isLoaded) {
      // User signed out, clear profile state
      setIsProfileComplete(false)
    }
  }, [isSignedIn, user, isLoaded])

  // Show loading while Clerk is initializing
  if (!isLoaded) {
    return <div>Loading...</div>
  }

  // Show authentication if not signed in
  if (!isSignedIn) {
    return <ClerkAuth onAuthSuccess={handleAuthSuccess} />
  }

  // Show profile setup if signed in but profile not complete
  if (isSignedIn && !isProfileComplete) {
    return <UserProfile onApiKeySet={handleApiKeySet} />
  }

  return (
    <div ref={containerRef} className="min-h-0">
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <ToastContext.Provider value={{ showToast }}>
            {view === "queue" ? (
              <Queue setView={setView} onSignOut={handleSignOut} />
            ) : view === "solutions" ? (
              <Solutions setView={setView} onSignOut={handleSignOut} />
            ) : (
              <></>
            )}
          </ToastContext.Provider>
          <Toast
            open={toastOpen}
            onOpenChange={setToastOpen}
            variant={toastMessage.variant}
            duration={3000}
          >
            <ToastTitle>{toastMessage.title}</ToastTitle>
            <ToastDescription>{toastMessage.description}</ToastDescription>
          </Toast>
          <ToastViewport />
        </ToastProvider>
      </QueryClientProvider>
    </div>
  )
}

export default App

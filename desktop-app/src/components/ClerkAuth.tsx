import { useRef, useEffect, useState } from "react"
import { SignIn, SignUp, useUser, useClerk } from "@clerk/clerk-react"
import { Button } from "./ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "./ui/card"

interface ClerkAuthProps {
  onAuthSuccess: () => void
  showSignUp?: boolean
  onToggleMode?: () => void
}

const ClerkAuth: React.FC<ClerkAuthProps> = ({
  onAuthSuccess,
  showSignUp = false,
  onToggleMode
}) => {
  const { isSignedIn, user, isLoaded } = useUser()
  const { signOut } = useClerk()
  const contentRef = useRef<HTMLDivElement>(null)
  const [debugInfo, setDebugInfo] = useState("")

  // Debug logging
  useEffect(() => {
    console.log("ClerkAuth - isLoaded:", isLoaded, "isSignedIn:", isSignedIn, "user:", user)
    setDebugInfo(`Loaded: ${isLoaded}, SignedIn: ${isSignedIn}, User: ${user?.emailAddresses[0]?.emailAddress || 'none'}`)
  }, [isLoaded, isSignedIn, user])

  useEffect(() => {
    // Height update logic for Electron window sizing
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    return () => {
      resizeObserver.disconnect()
    }
  }, [showSignUp])

  useEffect(() => {
    if (isSignedIn && user) {
      onAuthSuccess()
    }
  }, [isSignedIn, user, onAuthSuccess])

  const handleSignOut = async () => {
    await signOut()
  }

  // Show loading state while Clerk is initializing
  if (!isLoaded) {
    return (
      <div
        ref={contentRef}
        className="w-fit h-fit flex flex-col items-center justify-center bg-gray-50 rounded-xl"
      >
        <Card>
          <CardHeader className="space-y-2">
            <CardTitle className="text-2xl font-semibold text-center">
              Loading...
            </CardTitle>
            <CardDescription className="text-center text-gray-500">
              Initializing authentication system...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (isSignedIn && user) {
    return (
      <div
        ref={contentRef}
        className="w-fit h-fit flex flex-col items-center justify-center bg-gray-50 rounded-xl"
      >
        <Card>
          <CardHeader className="space-y-2">
            <CardTitle className="text-2xl font-semibold text-center">
              Welcome back, {user.firstName || user.emailAddresses[0]?.emailAddress}!
            </CardTitle>
            <CardDescription className="text-center text-gray-500">
              You are successfully signed in. Loading your profile...
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={handleSignOut}
              variant="outline"
              className="w-full font-medium"
            >
              Sign Out
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div
      ref={contentRef}
      className="w-fit h-fit flex flex-col items-center justify-center bg-gray-50 rounded-xl"
    >
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-semibold text-center">
            Welcome to Interview Coder
          </CardTitle>
          <CardDescription className="text-center text-gray-500">
            {showSignUp
              ? "Create your account to get started. Press Cmd + B to hide/show the window."
              : "Sign in to your account to continue. Press Cmd + B to hide/show the window."
            }
            <br />
            <small className="text-xs text-gray-400">Debug: {debugInfo}</small>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-center">
            {showSignUp ? (
              <SignUp 
                appearance={{
                  elements: {
                    formButtonPrimary: "bg-primary hover:bg-primary/90",
                    card: "shadow-none border-0",
                    headerTitle: "hidden",
                    headerSubtitle: "hidden"
                  }
                }}
                redirectUrl="/dashboard"
              />
            ) : (
              <SignIn 
                appearance={{
                  elements: {
                    formButtonPrimary: "bg-primary hover:bg-primary/90",
                    card: "shadow-none border-0",
                    headerTitle: "hidden",
                    headerSubtitle: "hidden"
                  }
                }}
                redirectUrl="/dashboard"
              />
            )}
          </div>

          {onToggleMode && (
            <div className="text-center">
              <Button
                type="button"
                variant="link"
                onClick={onToggleMode}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                {showSignUp
                  ? "Already have an account? Sign in"
                  : "Don't have an account? Sign up"
                }
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default ClerkAuth

import { useState, useRef, useEffect } from "react"
import { Input } from "./ui/input"
import { Button } from "./ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "./ui/card"
import { authHelpers } from "../lib/supabase"

interface SupabaseAuthProps {
  onAuthSuccess: (user: any) => void
}

const SupabaseAuth: React.FC<SupabaseAuthProps> = ({ onAuthSuccess }) => {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isSignUp, setIsSignUp] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Height update logic for Electron window sizing
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    return () => {
      resizeObserver.disconnect()
    }
  }, [isSignUp, error])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email.trim() || !password.trim()) return

    setIsLoading(true)
    setError("")

    try {
      let result
      if (isSignUp) {
        result = await authHelpers.signUp(email.trim(), password.trim())
      } else {
        result = await authHelpers.signIn(email.trim(), password.trim())
      }

      if (result.error) {
        // Handle specific error cases
        let errorMessage = result.error.message

        if (errorMessage.includes('Invalid login credentials')) {
          errorMessage = "Invalid email or password. Please check your credentials and try again."
        } else if (errorMessage.includes('Email not confirmed')) {
          errorMessage = "Please check your email and click the confirmation link before signing in."
        } else if (errorMessage.includes('User already registered')) {
          errorMessage = "An account with this email already exists. Please sign in instead."
          setIsSignUp(false) // Switch to sign in mode
        }

        throw new Error(errorMessage)
      }

      if (result.data.user) {
        // Store user info for the app
        const userData = {
          id: result.data.user.id,
          email: result.data.user.email,
          created_at: result.data.user.created_at
        }
        localStorage.setItem('supabase_user', JSON.stringify(userData))
        onAuthSuccess(userData)
      } else if (isSignUp) {
        // For sign up, user needs to confirm email
        setError("Account created! Please check your email and click the confirmation link before signing in.")
        setIsSignUp(false) // Switch to sign in mode
      }
    } catch (error: any) {
      console.error("Auth error:", error)
      setError(error.message || "Authentication failed. Please try again.")
      // Clear password on error for security
      setPassword("")
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    setError("")

    try {
      const { data, error } = await authHelpers.signInWithGoogle()
      if (error) {
        throw new Error(error.message)
      }

      if (data?.url) {
        // OAuth URL opened in external browser
        setError("Google sign-in opened in your browser. Please complete the authentication there and return to this app.")
        // Note: The actual authentication will be handled by the auth state change listener
      }
    } catch (error: any) {
      console.error("Google auth error:", error)
      setError(error.message || "Google authentication failed. Please try email and password.")
    } finally {
      setIsLoading(false)
    }
  }

  const toggleMode = () => {
    setIsSignUp(!isSignUp)
    setError("")
    setEmail("")
    setPassword("")
  }

  return (
    <div
      ref={contentRef}
      className="w-fit h-fit flex flex-col items-center justify-center"
    >
      <Card className="w-full max-w-md bg-white/95 backdrop-blur-sm border border-gray-200 shadow-lg">
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-semibold text-center text-gray-800">
            Welcome to Interview Coder
          </CardTitle>
          <CardDescription className="text-center text-gray-600">
            {isSignUp
              ? "Create your account to get started. Press Cmd + B to hide/show the window."
              : "Sign in to your account to continue. Press Cmd + B to hide/show the window."
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value)
                  setError("")
                }}
                className="w-full"
                disabled={isLoading}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value)
                  setError("")
                }}
                className="w-full"
                disabled={isLoading}
                required
                minLength={6}
              />
            </div>

            {error && (
              <p className="text-sm text-red-600">{error}</p>
            )}

            <Button
              type="submit"
              className="w-full font-medium"
              disabled={!email.trim() || !password.trim() || isLoading}
            >
              {isLoading
                ? (isSignUp ? "Creating Account..." : "Signing In...")
                : (isSignUp ? "Create Account" : "Sign In")
              }
            </Button>

            {/* Google OAuth temporarily disabled - will be re-enabled once proper credentials are configured */}
            {false && (
              <>
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-gray-50 px-2 text-gray-500">Or</span>
                  </div>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  onClick={handleGoogleSignIn}
                  className="w-full font-medium"
                  disabled={isLoading}
                >
                  <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Continue with Google
                </Button>
              </>
            )}

            <div className="text-center">
              <Button
                type="button"
                variant="link"
                onClick={toggleMode}
                className="text-sm text-gray-600 hover:text-gray-800"
                disabled={isLoading}
              >
                {isSignUp
                  ? "Already have an account? Sign in"
                  : "Don't have an account? Sign up"
                }
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

export default SupabaseAuth

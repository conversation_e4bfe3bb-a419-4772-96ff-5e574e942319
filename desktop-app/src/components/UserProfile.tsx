import { useState, useRef, useEffect } from "react"
import { useUser, useClerk } from "@clerk/clerk-react"
import { Input } from "./ui/input"
import { But<PERSON> } from "./ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "./ui/card"

interface UserProfileProps {
  onApiKeySet: () => void
}

const UserProfile: React.FC<UserProfileProps> = ({ onApiKeySet }) => {
  const { user } = useUser()
  const { signOut } = useClerk()
  const [apiKey, setApiKey] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [hasApiKey, setHasApiKey] = useState(false)
  const [error, setError] = useState("")
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Height update logic for Electron window sizing
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    return () => {
      resizeObserver.disconnect()
    }
  }, [hasApiKey, error])

  useEffect(() => {
    // Check if user already has API key
    const checkExistingApiKey = async () => {
      if (user?.publicMetadata?.openaiApiKey) {
        setHasApiKey(true)
        // Also store in electron store for local access
        await window.electronAPI.setApiKey(user.publicMetadata.openaiApiKey as string)
        onApiKeySet()
      } else {
        // Check local storage as fallback
        const localApiKey = await window.electronAPI.getApiKey()
        if (localApiKey) {
          setHasApiKey(true)
          onApiKeySet()
        }
      }
    }

    if (user) {
      checkExistingApiKey()
    }
  }, [user, onApiKeySet])

  const handleSaveApiKey = async () => {
    if (!apiKey.trim()) {
      setError("Please enter your OpenAI API key")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // Validate API key format
      if (!apiKey.startsWith('sk-')) {
        throw new Error("Invalid API key format. OpenAI API keys should start with 'sk-'")
      }

      // Store API key in Clerk user metadata
      await user.update({
        publicMetadata: {
          ...user.publicMetadata,
          openaiApiKey: apiKey.trim()
        }
      })

      // Also store in electron store for local access
      const result = await window.electronAPI.setApiKey(apiKey.trim())

      if (result.success) {
        setHasApiKey(true)
        onApiKeySet()
      } else {
        throw new Error(result.error || "Failed to save API key locally")
      }
    } catch (error: any) {
      console.error("Error saving API key:", error)
      setError(error.message || "Failed to save API key. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    await signOut()
    await window.electronAPI.clearStore()
  }

  if (hasApiKey) {
    return (
      <div
        ref={contentRef}
        className="w-fit h-fit flex flex-col items-center justify-center bg-gray-50 rounded-xl"
      >
        <Card>
          <CardHeader className="space-y-2">
            <CardTitle className="text-2xl font-semibold text-center">
              Profile Complete!
            </CardTitle>
            <CardDescription className="text-center text-gray-500">
              Your OpenAI API key has been saved. You can now use the application.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleSignOut}
              variant="outline"
              className="w-full font-medium"
            >
              Sign Out
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div
      ref={contentRef}
      className="w-fit h-fit flex flex-col items-center justify-center bg-gray-50 rounded-xl"
    >
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-semibold text-center">
            Complete Your Profile
          </CardTitle>
          <CardDescription className="text-center text-gray-500">
            Welcome, {user?.firstName || user?.emailAddresses[0]?.emailAddress}!
            <br />
            Please enter your OpenAI API key to get started.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Input
              type="password"
              placeholder="Enter your OpenAI API key (sk-...)"
              value={apiKey}
              onChange={(e) => {
                setApiKey(e.target.value)
                setError("")
              }}
              className="w-full"
              disabled={isLoading}
            />
            <p className="text-xs text-gray-500">
              Your API key will be stored securely and encrypted.
            </p>
          </div>

          {error && (
            <p className="text-sm text-red-600">{error}</p>
          )}

          <Button
            onClick={handleSaveApiKey}
            className="w-full font-medium"
            disabled={!apiKey.trim() || isLoading}
          >
            {isLoading ? "Saving..." : "Save API Key"}
          </Button>

          <div className="text-center">
            <Button
              onClick={handleSignOut}
              variant="link"
              className="text-sm text-gray-600 hover:text-gray-800"
              disabled={isLoading}
            >
              Sign Out
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default UserProfile

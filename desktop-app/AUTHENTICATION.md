# Remote Authentication Implementation

This document describes the remote authentication system implemented for Interview Coder using Supabase.

## Overview

The application uses **Supabase** for remote authentication instead of local API key storage. This provides:

- Secure user authentication with email/password and Google OAuth
- Remote storage of user preferences and API keys
- Cross-device synchronization
- Better security and user management
- Built-in Google OAuth support

## Architecture

### Components

1. **SupabaseAuth.tsx** - <PERSON>les sign-in/sign-up UI using Supabase authentication
2. **SupabaseUserProfile.tsx** - Manages user profile and OpenAI API key setup
3. **App.tsx** - Updated to use Supabase authentication state

### Authentication Flow

1. **User opens app** → SupabaseAuth component shows sign-in/sign-up
2. **User signs in** → Supabase validates credentials (email/password or Google OAuth)
3. **Profile check** → App checks if user has OpenAI API key configured
4. **API key setup** → If no key, SupabaseUserProfile component prompts for setup
5. **App ready** → User can use the application

### Data Storage

- **Supabase User Profiles**: Stores OpenAI API key securely in user's profile
- **Local Electron Store**: Caches API key locally for offline access
- **Sync**: API key syncs between Supabase and local storage

## Configuration

### Environment Variables

```bash
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

### Supabase Setup

1. Create Supabase project at https://supabase.com
2. Enable Google OAuth provider in Authentication settings
3. Configure redirect URLs for your application
4. Copy project URL and anon key to .env file

## Usage

### Running the App

```bash
# Development
npm run app:dev

# Production build
npm run build
```

### Authentication States

- **Not signed in**: Shows Supabase sign-in/sign-up interface
- **Signed in, no API key**: Shows profile setup to enter OpenAI API key
- **Signed in, has API key**: Shows main application interface

### User Management

Users can:
- Sign in with email/password or Google account
- Update their OpenAI API key
- Sign out (clears local data)
- Access their data from any device

## Security Features

- API keys stored encrypted in Clerk user metadata
- Local storage uses electron-store encryption
- No sensitive data in plain text
- Secure OAuth flow with Google

## Development Notes

### Testing Authentication

1. Start development server: `npm run app:dev`
2. App will show Clerk authentication interface
3. Sign in with Google account
4. Enter OpenAI API key when prompted
5. App will remember authentication state

### Debugging

- Check browser console for Supabase debug logs
- Verify environment variables are loaded
- Ensure Supabase project is configured correctly
- Check Google OAuth settings in Supabase dashboard

## Migration from Local Auth

The app automatically migrates from the old local API key system:
- Existing local API keys are preserved
- Users can sign in and re-enter their API key
- Old authentication components are replaced

## Future Enhancements

- User preferences sync (themes, shortcuts)
- Team collaboration features
- Usage analytics and insights
- Advanced user management

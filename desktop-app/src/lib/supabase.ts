import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false // Important for Electron
  }
})

// Database types
export interface UserProfile {
  id: string
  email: string
  openai_api_key?: string
  preferences?: Record<string, any>
  created_at: string
  updated_at: string
}

// Auth helper functions
export const authHelpers = {
  async signUp(email: string, password: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    return { data, error }
  },

  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  },

  async signInWithGoogle() {
    try {
      // For Electron, we need to open the OAuth URL in an external browser
      // and handle the callback through a custom protocol or local server
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'http://localhost:5173/auth/callback',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          }
        }
      })

      if (error) {
        throw error
      }

      // Open the OAuth URL in the default browser
      if (data.url) {
        window.electronAPI?.openExternal(data.url)
        return { data: { url: data.url }, error: null }
      }

      return { data, error }
    } catch (error: any) {
      return {
        data: null,
        error: {
          message: "Google OAuth failed. Please try email and password authentication."
        }
      }
    }
  },

  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  },

  async getSession() {
    const { data: { session }, error } = await supabase.auth.getSession()
    return { session, error }
  },

  async getUserProfile(userId: string): Promise<{ data: UserProfile | null, error: any }> {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    return { data, error }
  },

  async updateUserProfile(userId: string, updates: Partial<UserProfile>) {
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        id: userId,
        updated_at: new Date().toISOString(),
        ...updates
      })
      .select()
      .single()

    return { data, error }
  },

  async saveApiKey(userId: string, apiKey: string) {
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        id: userId,
        openai_api_key: apiKey,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    return { data, error }
  }
}
